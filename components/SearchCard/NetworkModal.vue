<template>
  <Teleport to="body">
    <transition name="fade">
      <div
        v-if="show"
        class="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50 mt-0!"
        @click.self="close"
      >
        <div
          class="relative bg-white dark:bg-[#141415] dark:border dark:border-[#27282D] dark:shadow-[0px_3px_8px_0px_#0000001A] rounded-[30px] w-[800px] h-[707px] select-none p-4"
          @click.stop
        >
          <div class="text-[20px] font-semibold mb-4 dark:text-white" style="font-weight: 600;">Network</div>
          
          <!-- Close button -->
          <div
            class="absolute top-4 right-4 wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white"
            @click="close"
          ></div>
          
          <!-- 加载状态 -->
          <div v-if="loading" class="flex flex-col items-center justify-center h-[600px]">
            <div class="loading-animation-container">
              <svg class="network-loading-svg" width="400" height="400" viewBox="-10 -10 420 420">
                <!-- 外圈大圆 -->
                <circle class="outer-circle-animation" cx="200" cy="200" r="200" />
                <!-- 内圈圆 -->
                <circle class="inner-circle-animation" cx="200" cy="200" r="130" />
                <!-- 装饰性路径 -->
                <path
                  class="decorative-path-animation"
                  d="M322.696 188.215C325.266 167.898 323.81 147.274 318.409 127.52C313.009 107.766 303.771 89.2696 291.223 73.0859C278.674 56.9022 263.061 43.3486 245.274 33.1989C227.488 23.0493 207.876 16.5023 187.56 13.9319C167.243 11.3614 146.619 12.8178 126.865 18.2179C107.111 23.618 88.6142 32.8561 72.4305 45.4046C56.2468 57.9531 42.6932 73.5664 32.5436 91.3529C22.3939 109.14 15.847 128.751 13.2765 149.068C10.7061 169.385 12.1625 190.009 17.5626 209.762C22.9627 229.516 32.2007 248.013 44.7493 264.197C57.2978 280.381 72.9111 293.934 90.6976 304.084C108.484 314.233 128.096 320.78 148.412 323.351C168.729 325.921 189.353 324.465 209.107 319.065C228.861 313.665 247.358 304.427 263.542 291.878C279.725 279.30 293.279 263.716 303.428 245.93C313.578 228.143 320.125 208.532 322.696 188.215L322.696 188.215Z"
                  stroke-width="21.8856" stroke-dasharray="0.51 5.09"
                  transform="translate(32.01, 31.36)" />
                
                <!-- 网络节点 -->
                <circle class="network-node-animation node-1" cx="120" cy="150" r="15" />
                <circle class="network-node-animation node-2" cx="280" cy="150" r="15" />
                <circle class="network-node-animation node-3" cx="150" cy="280" r="15" />
                <circle class="network-node-animation node-4" cx="250" cy="280" r="15" />
                <circle class="network-node-animation node-5" cx="200" cy="100" r="15" />
                
                <!-- 连接线 -->
                <line class="connection-line-animation" x1="200" y1="200" x2="115" y2="145" />
                <line class="connection-line-animation" x1="200" y1="200" x2="285" y2="145" />
                <line class="connection-line-animation" x1="200" y1="200" x2="145" y2="285" />
                <line class="connection-line-animation" x1="200" y1="200" x2="255" y2="285" />
                <line class="connection-line-animation" x1="200" y1="200" x2="200" y2="95" />
                
                <!-- 中心节点 (移到最后，确保在最上层) -->
                <circle class="center-node-animation" cx="200" cy="200" r="25" />
              </svg>
            </div>
            <div class="loading-text text-gray-500 dark:text-gray-400 mt-6">Loading network data...</div>
            
            <!-- 操作提示 - 轮播式 -->
            <div class="loading-tip text-xs text-gray-500 dark:text-gray-400 mt-4 text-center h-5 overflow-hidden">
              <Transition name="tip-fade" mode="out-in">
                <div :key="currentTipIndex" class="tip-item">{{ tips[currentTipIndex] }}</div>
              </Transition>
            </div>
          </div>
          
          <!-- 错误状态 -->
          <div v-else-if="error" class="flex items-center justify-center h-[600px]">
            <div class="text-red-500">{{ error }}</div>
          </div>
          
          <!-- 网络图表 -->
          <svg v-else-if="localCenterUser && localNearbyUsers.length >= 0" class="w-[700px] h-[600px] mx-auto" viewBox="0 0 800 700" @click="clearHover">
            <!-- Center Avatar Background - Moved to the first position -->
            <image
              :href="bgCircle"
              :x="centerSwayX - CENTER_BG_SIZE / 2"
              :y="centerSwayY - CENTER_BG_SIZE / 2"
              :width="CENTER_BG_SIZE"
              :height="CENTER_BG_SIZE"
              preserveAspectRatio="xMidYMid slice"
              class="opacity-30"
            />
            
            <!-- Lines with animation -->
            <line
              v-for="(user, i) in localNearbyUsers"
              :class="'line-transition'"
              :key="user.id"
              :x1="centerSwayX"
              :y1="centerSwayY"
              :x2="linesVisible ? getX(i) : center.x"
              :y2="linesVisible ? getY(i) : center.y"
              :stroke="activeId === user.id ? '#CB7C5D' : getLineColor()"
              :stroke-width="activeId === user.id ? 5 : 3"
              :style="lineStyle(user.id)"
              class="transition-all duration-300"
            />

            <!-- Center Avatar (mask-based for perfect circular image) -->
            <g>
              <!-- 透明的更大触发区域 -->
              <circle
                :cx="centerSwayX"
                :cy="centerSwayY"
                :r="CENTER_SIZE / 2 + 15"
                fill="transparent"
                @mouseenter="handleHover(localCenterUser, centerSwayX, centerSwayY)"
                @mouseleave="handleLeave"
              />
              <mask :id="`mask_center`">
                <circle :cx="centerSwayX" :cy="centerSwayY" :r="CENTER_SIZE / 2" fill="white" />
              </mask>
              <image
                v-if="localCenterUser?.avatar"
                :key="localCenterUser?.id"
                :href="getSafeAvatarUrl(localCenterUser.avatar)"
                :x="centerSwayX - CENTER_SIZE / 2"
                :y="centerSwayY - CENTER_SIZE / 2"
                :width="CENTER_SIZE"
                :height="CENTER_SIZE"
                preserveAspectRatio="xMidYMid slice"
                :mask="`url(#mask_center)`"
                :style="{ transition: 'all 150ms', ...avatarStyle(localCenterUser?.id) }"
                class="pointer-events-none"
              />
              <circle
                :cx="centerSwayX"
                :cy="centerSwayY"
                :r="CENTER_SIZE / 2"
                fill="none"
                :stroke="getCenterBorderColor()"
                stroke-width="6"
                class="pointer-events-none"
              />
              <text
                :key="localCenterUser.id"
                :x="centerSwayX"
                :y="centerSwayY + 25"
                text-anchor="middle"
                dominant-baseline="middle"
                font-size="18"
                fill="#fff"
                style="
                  pointer-events: auto;
                  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                "
                @mouseenter="handleHover(localCenterUser, centerSwayX, centerSwayY)"
                @mouseleave="handleLeave"
              >
                <tspan
                  v-for="(word, index) in localCenterUser.name.split(' ')"
                  :key="index"
                  :dy="index === 0 ? '0em' : '1.2em'"
                  :x="centerSwayX"
                >
                  {{ word }}
                </tspan>
              </text>
            </g>


            <!-- Avatar Patterns -->
            <defs>
              <template v-for="(user, i) in localNearbyUsers" :key="user.id">
                <pattern
                  :id="`avatar_${user.id}`"
                  patternUnits="userSpaceOnUse"
                  :width="SMALL_SIZE"
                  :height="SMALL_SIZE"
                >
                  <image
                    :href="getSafeAvatarUrl(user.avatar)"
                    x="0"
                    y="0"
                    :width="SMALL_SIZE"
                    :height="SMALL_SIZE"
                    preserveAspectRatio="xMidYMid slice"
                    @click.stop="handleClick(user, getX(i), getY(i))"
                  />
                </pattern>
              </template>
            </defs>

            <!-- Small Avatars -->
            <g v-for="(user, i) in localNearbyUsers" :key="user.id">
              <!-- 透明的更大触发区域 -->
              <g :transform="`translate(${getX(i) - SMALL_SIZE/2}, ${getY(i) - SMALL_SIZE/2})`">
                <circle
                  :cx="SMALL_SIZE/2"
                  :cy="SMALL_SIZE/2"
                  :r="SMALL_SIZE / 2 + 10"
                  fill="transparent"
                  class="cursor-pointer"
                  @click.stop="moveToCenter(user, i)"
                  @mouseenter="handleHover(user, getX(i), getY(i))"
                  @mouseleave="handleLeave"
                />
                <!-- 实际的头像 -->
                <circle
                  :cx="SMALL_SIZE/2"
                  :cy="SMALL_SIZE/2"
                  :r="SMALL_SIZE / 2"
                  :fill="`url(#avatar_${user.id})`"
                  :stroke="activeId === user.id ? '#CB7C5D' : getAvatarBorderColor()"
                  :stroke-width="activeId === user.id ? 3 : 1"
                  :style="avatarStyle(user.id)"
                  class="relative transition-all duration-150 pointer-events-none"
                />
                <!-- 文字标签 -->
                <text
                  :x="SMALL_SIZE/2"
                  :y="SMALL_SIZE/2 + 15"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  font-size="14"
                  fill="#fff"
                  style="
                    pointer-events: auto;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                  "
                  class="cursor-pointer"
                  @click.stop="moveToCenter(user, i)"
                  @mouseenter="handleHover(user, getX(i), getY(i))"
                  @mouseleave="handleLeave"
                >
                  <tspan
                    v-for="(word, index) in user.name.split(' ')"
                    :key="index"
                    :dy="index === 0 ? '0em' : '1.2em'"
                    :x="SMALL_SIZE/2"
                  >
                    {{ word }}
                  </tspan>
                </text>
              </g>
            </g>
          </svg>

          <!-- Tooltip -->
          <div
            v-if="hoveredUser"
            class="absolute z-10 bg-[#F8F8F8] border border-[#E0E0E0] dark:border-[#333] rounded-lg shadow-md p-3 w-[380px] pointer-events-auto dark:bg-[#141415] dark:text-white"
            :style="{ top: tooltipY + 'px', left: tooltipX + 'px' }"
            @mouseenter="handleTooltipEnter"
            @mouseleave="handleTooltipLeave"
          >
            <div class="flex items-center mb-2 bg-[url('/image/comparecard.png')] dark:bg-[url('/image/comparecard-dark.png')] bg-cover bg-no-repeat bg-center p-3 rounded-lg">
              <div class="tooltip-avatar">
                <img
                  :src="getSafeAvatarUrl(hoveredUser.avatar)"
                  class="w-full h-full rounded-full object-cover"
                  alt="Avatar"
                  @error="handleAvatarError"
                />
              </div>
              <div class="ml-2 flex-1">
                <div class="font-semibold text-sm dark:text-white">{{ hoveredUser.name }}</div>
                <div class="tooltip-description text-xs text-gray-600 mt-1">
                  {{ hoveredUser.description }}
                </div>
              </div>
            </div>
            <button
              class="flex items-center justify-center rounded w-full h-[32px] text-3.5 bg-black text-white text-sm font-medium border-2 border-black hover:bg-black/90 transition-all dark:text-[#FAF9F5] dark:bg-[#000000] dark:border dark:border-[#FAF9F5]"
              @click="() => handleAnalyze(hoveredUser.id)"
            >
              <SvgIcon name="magic-wand" class="w-6 h-6 mr-2" />
              Analyze
            </button>
          </div>

          <!-- 下方按钮 -->
          <div class="fx-cer gap-4 absolute bottom-4 left-1/2 transform -translate-x-1/2 z-12">
            <!-- <div
              class="fx-cer justify-center w-8 h-8 border-1 border-[#ccc] rounded-1 cursor-pointer"
              @click="close"
            >
              <img :src="Arrowleft" alt="" class="w-6 h-6" />
            </div> -->
            <div
              class="fx-cer justify-center w-10 h-10 bg-[#F9E1D8] rounded-full cursor-pointer border border-[#F9E1D8] hover:bg-[#CB7C5D] hover:border-[#CB7C5D] transition-all duration-200 dark:bg-transparent dark:border-[#3E3E3E] dark:hover:bg-[#3E3E3E] dark:hover:border-[#3E3E3E]"
              @click="resetToDefault"
            >
              <SvgIcon name="refresh" class="!w-5 !h-5 text-[#CB7C5D] dark:text-[#3E3E3E] dark:hover:text-white" />
            </div>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import bgCircle from '../../assets/svg/BG-circle.svg'
  import Arrowleft from '../../assets/image/Arrowleft.png'
  import { getGraph, getNetwork } from '~/api'

  const router = useRouter()
  const { currentUser } = useFirebaseAuth()
  const props = defineProps({
    show: Boolean,
    currentCandidate: Object, // 当前候选人信息，包含author_ids
  })
  const emit = defineEmits(['update:show'])

  const CENTER_SIZE = 150 // 中心头像大小
  const CENTER_BG_SIZE = 450 // 中心背景图片大小
  const SMALL_SIZE = 100
  const RADIUS = 320 // 调整半径值，直径300px
  const center = { x: 396, y: 350 } // 调整中心点坐标让内容居中
  
  // 浮窗定位相关常量
  const TOOLTIP_WIDTH = 380
  const TOOLTIP_HEIGHT = 150
  const TOOLTIP_MARGIN = 15 // 浮窗与头像之间的标准间距
  const CENTER_TOOLTIP_MARGIN = 0 // 中心头像的浮窗间距，更近一些

  const hoveredUser = ref(null)
  const activeId = ref(null)
  const hoveringTooltip = ref(false)
  const tooltipX = ref(0)
  const tooltipY = ref(0)
  const leaveTimer = ref(null) // 用于存储延迟隐藏的计时器ID
  const movingUser = ref(null)
  const movingIndex = ref(-1)
  const movingX = ref(0)
  const movingY = ref(0)
  const fading = ref(false)
  const linesVisible = ref(true)
  const animationTime = ref(0)

  // API数据状态
  const loading = ref(false)
  const error = ref(null)
  const networkUsers = ref([])

  const localUsers = ref([])
  const localCenterUser = ref(null)
  const localNearbyUsers = ref([])
  
  // 初始状态缓存 - 只在第一次加载时保存，关闭卡片时清理
  const initialState = ref(null)

  // 当前数据类型信息 - 用于moveToCenter时的套娃搜索
  const currentBuilderInfo = ref(null)
  
  // 轮播提示相关
  const tips = ref([
    "Hover over avatars to view talent profiles",
    "Click on an avatar to explore their network",
    "Use the refresh button to return to the initial network"
  ])
  const currentTipIndex = ref(0)
  let tipInterval = null
  
  // 轮播提示切换函数
  const rotateTips = () => {
    currentTipIndex.value = (currentTipIndex.value + 1) % tips.value.length
  }

  // 获取网络数据 - 新版本，支持初始Network API调用
  const fetchNetworkDataNew = async (candidate) => {
    if (!candidate || !currentUser.value) return

    loading.value = true
    error.value = null

    try {
      let networkParams = null
      let dataType = 'paper' // 默认类型

      // 根据候选人数据确定Network API参数
      if (candidate.profile) {
        // 新的search API数据结构，使用dataset字段（向后兼容builder字段）
        const dataset = candidate.dataset || candidate.builder
        if (dataset === 'github') {
          networkParams = {
            network_id: candidate.profile.github
          }
          dataType = 'github'
        } else if (dataset === 'scholar') {
          if (candidate.group === 'company') {
            networkParams = {
              network_id: candidate.profile.id
            }
            dataType = 'paper' // company类型仍使用paper的网络数据
          } else {
            networkParams = {
              network_id: candidate.profile.openreview
            }
            dataType = 'paper'
          }
        }
      }

      if (!networkParams || !networkParams.network_id) {
        throw new Error('Unable to extract network ID from candidate data')
      }

      const response = await getNetwork(networkParams, currentUser.value.uid)
      networkUsers.value = response.data || []

      // 确定中心用户信息
      const centerUser = {
        id: candidate.id || 'center',
        name: candidate.name || 'Current User',
        avatar: candidate.avatarUrl || '/image/avator.png',
        description: 'Current candidate',
        openreview_id: networkParams.network_id,
        scholar_id: candidate.id,
        data_type: dataType
      }

      // 转换网络用户数据，现在使用统一的network_id结构
      const networkUsersData = networkUsers.value.map(user => ({
        id: user.analyze_id || user.id, // 使用analyze_id作为显示ID
        name: user.name,
        avatar: user.avatar_url,
        description: user.position,
        openreview_id: user.network_id, // 使用network_id
        scholar_id: user.analyze_id, // 使用analyze_id
        data_type: 'paper', // 默认类型
        data_id: user.network_id,
        analyze_id: user.analyze_id,
        network_id: user.network_id, // 保留network_id字段用于moveToCenter
        builder: 'scholar' // 默认builder
      }))

      // 设置本地状态
      localUsers.value = [centerUser, ...networkUsersData]
      localCenterUser.value = centerUser
      localNearbyUsers.value = networkUsersData
      originalUsers.value = [centerUser, ...networkUsersData]

      // 保存初始状态（仅在第一次加载时）
      if (!initialState.value) {
        initialState.value = {
          centerUser: { ...centerUser },
          nearbyUsers: [...networkUsersData]
        }
      }

      // 保存当前dataset信息，用于moveToCenter的套娃搜索
      currentBuilderInfo.value = {
        dataset: candidate.dataset || candidate.builder, // 使用新的dataset字段，向后兼容builder
        builder: candidate.dataset || candidate.builder, // 保持向后兼容性
        group: candidate.group,
        networkParams: networkParams
      }

    } catch (err) {
      error.value = err.message || 'Failed to load network data'

      // 如果API失败，至少显示当前候选人
      const fallbackUser = {
        id: candidate.id || 'center',
        name: candidate.name || 'Current User',
        avatar: candidate.avatarUrl || '/image/avator.png',
        description: 'Current candidate',
        openreview_id: 'fallback',
        scholar_id: candidate.id,
        data_type: 'paper'
      }

      localUsers.value = [fallbackUser]
      localCenterUser.value = fallbackUser
      localNearbyUsers.value = []
      originalUsers.value = [fallbackUser]

      // 如果没有初始状态，也保存fallback状态
      if (!initialState.value) {
        initialState.value = {
          centerUser: { ...fallbackUser },
          nearbyUsers: []
        }
      }
    } finally {
      loading.value = false
    }
  }

  // 获取网络数据 - 用于moveToCenter的套娃搜索
  const fetchNetworkDataForMoveToCenter = async (user) => {
    if (!user || !currentUser.value) return

    loading.value = true
    error.value = null

    try {
      // 使用网络API返回的network_id进行套娃搜索
      const networkId = user.network_id || user.id

      if (!networkId) {
        throw new Error('Unable to determine network_id for moveToCenter')
      }

      const networkParams = {
        network_id: networkId
      }

      console.log('MoveToCenter Network API call:', networkParams)

      const response = await getNetwork(networkParams, currentUser.value.uid)
      networkUsers.value = response.data || []

      // 确定新的中心用户信息
      const centerUser = {
        id: user.analyze_id || user.id,
        name: user.name,
        avatar: user.avatar,
        description: user.description,
        openreview_id: user.network_id,
        scholar_id: user.analyze_id,
        data_type: 'paper',
        analyze_id: user.analyze_id,
        network_id: user.network_id
      }

      // 转换网络用户数据
      const networkUsersData = networkUsers.value.map(networkUser => ({
        id: networkUser.analyze_id || networkUser.id, // 使用analyze_id作为显示ID
        name: networkUser.name,
        avatar: networkUser.avatar_url,
        description: networkUser.position,
        openreview_id: networkUser.network_id, // 使用network_id
        scholar_id: networkUser.analyze_id, // 使用analyze_id
        data_type: 'paper', // 默认类型
        data_id: networkUser.network_id,
        analyze_id: networkUser.analyze_id,
        network_id: networkUser.network_id, // 保留network_id字段用于moveToCenter
        builder: 'scholar' // 默认builder
      }))

      // 设置本地状态
      localUsers.value = [centerUser, ...networkUsersData]
      localCenterUser.value = centerUser
      localNearbyUsers.value = networkUsersData
      originalUsers.value = [centerUser, ...networkUsersData]

    } catch (err) {
      error.value = err.message || 'Failed to load network data for moveToCenter'
      console.error('MoveToCenter Network API error:', err)

      // 如果API失败，至少显示当前用户
      const fallbackUser = {
        id: user.id || 'center',
        name: user.name || 'Current User',
        avatar: user.avatar || '/image/avator.png',
        description: user.description || 'Current user',
        openreview_id: user.id,
        scholar_id: user.id,
        data_type: user.data_type || 'paper'
      }

      localUsers.value = [fallbackUser]
      localCenterUser.value = fallbackUser
      localNearbyUsers.value = []
      originalUsers.value = [fallbackUser]
    } finally {
      loading.value = false
    }
  }

  // 获取网络数据 - 旧版本，用于点击周边头像的情况
  const fetchNetworkData = async (openreviewId, centerUserInfo = null, dataType = 'paper') => {
    if (!openreviewId) return

    loading.value = true
    error.value = null

    try {
      const response = await getGraph({
        user: openreviewId,
        type: dataType
      })
      networkUsers.value = response.data || []
      
      // 确定中心用户信息
      let centerUser
      if (centerUserInfo) {
        // 如果提供了中心用户信息（点击周围头像的情况）
        centerUser = {
          id: centerUserInfo.scholar_id,
          name: centerUserInfo.name,
          avatar: centerUserInfo.avatar,
          description: centerUserInfo.description,
          openreview_id: centerUserInfo.openreview_id,
          scholar_id: centerUserInfo.scholar_id,
          data_type: centerUserInfo.data_type // 保持原有的data_type
        }
      } else {
        // 初始情况，使用当前候选人
        centerUser = {
          id: props.currentCandidate?.id || 'center',
          name: props.currentCandidate?.name || 'Current User',
          avatar: props.currentCandidate?.avatarUrl || '/image/avator.png',
          description: 'Current candidate',
          openreview_id: openreviewId,
          scholar_id: props.currentCandidate?.id,
          data_type: dataType // 使用传入的dataType参数
        }
      }
      
      // 转换网络用户数据，现在paper和github两种类型使用统一格式
      const networkUsersData = networkUsers.value.map(user => ({
        id: user.id, // 直接使用网络API返回的id字段
        name: user.name,
        avatar: user.avatar_url,
        description: user.position,
        openreview_id: user.id, // 使用网络API返回的id字段
        scholar_id: user.id, // 使用网络API返回的id字段
        data_type: user.data_type,
        data_id: user.data_id, // 保留原始data_id字段
        analyze_id: user.analyze_id, // 保留analyze_id字段
        builder: user.builder // 保留builder字段
      }))
      
      // 去重处理：
      // 1. 过滤掉与中心用户同名的用户
      // 2. 对于重复姓名，只保留第一个
      const centerUserName = centerUser.name.toLowerCase()
      const seenNames = new Set()
      const filteredNetworkUsersData = networkUsersData.filter(user => {
        const userName = user.name.toLowerCase()
        
        // 如果与中心用户同名，则过滤掉
        if (userName === centerUserName) {
          return false
        }
        
        // 如果名字已经出现过，则过滤掉
        if (seenNames.has(userName)) {
          return false
        }
        
        // 记录这个名字并保留这个用户
        seenNames.add(userName)
        return true
      })
      
      // 组合所有用户，中心用户在第一位
      const mappedUsers = [centerUser, ...filteredNetworkUsersData]
      
      localUsers.value = mappedUsers
      localCenterUser.value = mappedUsers[0]
      localNearbyUsers.value = mappedUsers.slice(1)
      originalUsers.value = [...mappedUsers]
      
      // 只在第一次加载时保存初始状态（没有centerUserInfo表示是初始加载）
      if (!centerUserInfo && !initialState.value) {
        initialState.value = {
          centerUser: { ...mappedUsers[0] },
          nearbyUsers: [...mappedUsers.slice(1)]
        }
      }
      
    } catch (err) {
      error.value = err.message || 'Failed to load network data'
      
      // 如果API失败，至少显示当前候选人
      const fallbackUser = {
        id: props.currentCandidate?.id || 'center',
        name: props.currentCandidate?.name || 'Current User',
        avatar: props.currentCandidate?.avatarUrl || '/image/avator.png',
        description: 'Current candidate',
        openreview_id: openreviewId,
        scholar_id: props.currentCandidate?.id,
        data_type: dataType, // 使用传入的dataType参数
        data_id: dataType === 'paper' ? openreviewId : undefined // 为paper类型保留data_id
      }
      
      localUsers.value = [fallbackUser]
      localCenterUser.value = fallbackUser
      localNearbyUsers.value = []
      originalUsers.value = [fallbackUser]
      
      // 如果是初始加载且没有初始状态，也保存fallback状态
      if (!centerUserInfo && !initialState.value) {
        initialState.value = {
          centerUser: { ...fallbackUser },
          nearbyUsers: []
        }
      }
    } finally {
      loading.value = false
    }
  }

  // 监听当前候选人变化和模态框显示状态
  watch(
    () => [props.currentCandidate, props.show],
    ([newCandidate, showModal]) => {
      if (showModal && newCandidate) {
        // 优先使用新的Network API
        if (newCandidate.profile) {
          // 新的search API数据结构，使用新的Network API
          fetchNetworkDataNew(newCandidate)
        } else if (newCandidate.author_ids) {
          // 兼容旧的数据结构，使用旧的Graph API
          const dataType = newCandidate.author_ids.startsWith('~') ? 'paper' : 'github'
          fetchNetworkData(newCandidate.author_ids, null, dataType)
        }
      }
    },
    { immediate: true }
  )

  let animationId = null

  onMounted(() => {
    nextTick(() => {
      linesVisible.value = true
    })
    
    // 启动动画循环
    const animate = () => {
      animationTime.value += 0.02
      animationId = requestAnimationFrame(animate)
    }
    animate()
    
    // 启动提示轮播
    tipInterval = setInterval(rotateTips, 3000) // 每3秒切换一次提示
  })

  onUnmounted(() => {
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
    
    // 清除提示轮播定时器
    if (tipInterval) {
      clearInterval(tipInterval)
    }
    
    // 清除hover延迟计时器
    if (leaveTimer.value) {
      clearTimeout(leaveTimer.value)
    }
  })

  const positions = computed(() => {
    const n = localNearbyUsers.value.length
    // 当有5个结果时，使用更小的半径避免最下面的头像被遮挡
    const currentRadius = n === 5 ? RADIUS - 60 : RADIUS
    
    return localNearbyUsers.value.map((_, i) => {
      // 基础角度计算：居中对称分布 + 45度旋转
      const baseAngle = (2 * Math.PI * i) / n - Math.PI / 2 + Math.PI / 4
      
      // 只有当头像数量为3时，才应用20度逆时针旋转
      let angle = baseAngle
      if (n === 3) {
        const rotationAngle = -20 * Math.PI / 180 // -20度转换为弧度（负号表示逆时针）
        angle = baseAngle + rotationAngle
      }
      
      return {
        x: center.x + currentRadius * Math.cos(angle),
        y: center.y + currentRadius * Math.sin(angle),
      }
    })
  })

  function getX(i) {
    if (movingUser.value && localNearbyUsers.value[i].id === movingUser.value.id) {
      return movingX.value
    }
    
    // 在淡出动画期间暂停摇晃
    if (fading.value) {
      return positions.value[i].x
    }
    
    // 添加水平摇晃效果，每个球有不同的相位
    const baseX = positions.value[i].x
    const swayAmplitude = 5 // 减小摇晃幅度 (从8改为5)
    const swaySpeed = 0.6 // 稍微减慢摇晃速度 (从0.8改为0.6)
    const phaseOffset = i * 1.5 // 保持相位偏移不变
    const sway = Math.sin(animationTime.value * swaySpeed + phaseOffset) * swayAmplitude
    
    return baseX + sway
  }

  function getY(i) {
    if (movingUser.value && localNearbyUsers.value[i].id === movingUser.value.id) {
      return movingY.value
    }
    
    // 在淡出动画期间暂停摇晃
    if (fading.value) {
      return positions.value[i].y
    }
    
    // 添加垂直摇晃效果，使用不同的频率和相位
    const baseY = positions.value[i].y
    const swayAmplitude = 4 // 减小垂直摇晃幅度 (从6改为4)
    const swaySpeed = 0.5 // 稍微减慢垂直摇晃速度 (从0.6改为0.5)
    const phaseOffset = i * 2.1 // 保持相位偏移不变
    const sway = Math.sin(animationTime.value * swaySpeed + phaseOffset) * swayAmplitude
    
    return baseY + sway
  }

  function getRadius(id) {
    if (movingUser.value && id === movingUser.value.id) {
      const progress = getMoveProgress()
      return SMALL_SIZE / 2 + (CENTER_SIZE / 2 - SMALL_SIZE / 2) * progress
    }
    return SMALL_SIZE / 2
  }

  // 中心头像的摇晃位置
  const centerSwayX = computed(() => {
    if (fading.value || movingUser.value) return center.x
    const swayAmplitude = 2 // 减小中心头像摇晃幅度 (从4改为2)
    const swaySpeed = 0.4 // 稍微减慢摇晃速度 (从0.5改为0.4)
    const sway = Math.sin(animationTime.value * swaySpeed) * swayAmplitude
    return center.x + sway
  })

  const centerSwayY = computed(() => {
    if (fading.value || movingUser.value) return center.y
    const swayAmplitude = 1.5 // 减小垂直摇晃幅度 (从3改为1.5)
    const swaySpeed = 0.3 // 减慢垂直摇晃速度 (从0.4改为0.3)
    const phaseOffset = 1.2 // 保持相位差不变
    const sway = Math.sin(animationTime.value * swaySpeed + phaseOffset) * swayAmplitude
    return center.y + sway
  })

  function getMoveProgress() {
    const dx = movingX.value - center.x
    const dy = movingY.value - center.y
    const dist = Math.sqrt(dx * dx + dy * dy)
    return Math.min(1, 1 - dist / RADIUS)
  }

  function calculateTextY(y, radius, lineCount) {
    return y // 保持函数存在但简化它，以防其他地方还在调用
  }

  function handleClick(user, x, y) {
    // 直接复用 handleHover 的全部逻辑
    handleHover(user, x, y);

    // 如果 handleClick 还有其他特殊逻辑，可以加在这里
  }

  function handleHover(user, x, y) {
    // 清除任何可能存在的延迟隐藏计时器
    if (leaveTimer.value) {
      clearTimeout(leaveTimer.value)
      leaveTimer.value = null
    }
    
    if (fading.value || movingUser.value) return // 只有在动画过程中才阻止 hover
    
    // 立即设置强调效果
    activeId.value = user.id

    // 计算头像半径
    const avatarRadius = user.id === localCenterUser.value?.id ? CENTER_SIZE / 2 : SMALL_SIZE / 2
    
    // 根据是否为中心头像选择不同的间距
    const margin = user.id === localCenterUser.value?.id ? CENTER_TOOLTIP_MARGIN : TOOLTIP_MARGIN
    
    // 1. 计算水平位置 (保持居中)
    let newX = x - TOOLTIP_WIDTH / 2
    
    // 2. 智能计算垂直位置
    let newY;
    
    // 检查上方是否有足够空间 (头像顶部 - 间距 - 浮窗高度)
    const spaceAbove = y - avatarRadius - margin - TOOLTIP_HEIGHT
    
    if (spaceAbove > 0) {
      // 空间足够，首选位置：在头像上方
      newY = y - avatarRadius - margin - TOOLTIP_HEIGHT
    } else {
      // 空间不足，备选位置：在头像下方
      newY = y + avatarRadius + margin
    }

    // 3. 边界检查，防止浮窗超出容器
    
    // 获取 SVG 画布的尺寸 (w-[700px] h-[600px] mx-auto, 在 800x707 的容器里)
    // 水平边界 ( (800-700)/2 = 50 到 750 )
    const containerPadding = 4 * 4; // p-4
    const svgOffsetX = 50 + containerPadding;
    const svgOffsetY = (707 - 600) / 2 + containerPadding; // 粗略计算

    // 水平边界修正
    if (newX < svgOffsetX) {
      newX = svgOffsetX
    }
    if (newX + TOOLTIP_WIDTH > svgOffsetX + 700) {
      newX = svgOffsetX + 700 - TOOLTIP_WIDTH
    }
    
    // 垂直边界修正
    if (newY < svgOffsetY) {
      newY = svgOffsetY
    }
    if (newY + TOOLTIP_HEIGHT > svgOffsetY + 600) {
      // 如果下方也超出，重新计算，将其紧贴底部
      newY = svgOffsetY + 600 - TOOLTIP_HEIGHT
    }

    tooltipX.value = newX
    tooltipY.value = newY
    hoveredUser.value = user
  }

  function moveToCenter(user, index) {
    if (movingUser.value) return

    // 立即隐藏浮窗并清理相关状态
    hoveredUser.value = null
    activeId.value = null
    hoveringTooltip.value = false
    if (leaveTimer.value) {
      clearTimeout(leaveTimer.value)
      leaveTimer.value = null
    }

    movingUser.value = user
    movingIndex.value = index
    movingX.value = positions.value[index].x
    movingY.value = positions.value[index].y
    linesVisible.value = false
    fadeOther(true)

    // 使用新的Network API进行套娃搜索
    if (currentBuilderInfo.value && currentUser.value) {
      fetchNetworkDataForMoveToCenter(user)
    }

    nextTick(() => {
      setTimeout(() => {
        movingX.value = center.x
        movingY.value = center.y
      }, 16)
      setTimeout(() => {
        finishMove()
      }, 800)
    })
  }

  function fadeOther(flag) {
    fading.value = flag
  }

  function finishMove() {
    // 清理动画状态，数据已经在fetchNetworkData中正确设置
    movingUser.value = null
    movingIndex.value = -1
    fading.value = false
    activeId.value = null
    linesVisible.value = true
  }

  function handleLeave() {
    // 清除任何旧的计时器，以防万一
    if (leaveTimer.value) {
      clearTimeout(leaveTimer.value)
    }
    // 设置一个新的计时器来隐藏浮窗
    leaveTimer.value = setTimeout(() => {
      hoveredUser.value = null
      activeId.value = null
    }, 300) // 增加延迟到 300ms，给用户更多时间移动鼠标
  }

  function handleTooltipEnter() {
    hoveringTooltip.value = true
    // 当鼠标进入浮窗时，清除"准备隐藏"的计时器
    if (leaveTimer.value) {
      clearTimeout(leaveTimer.value)
      leaveTimer.value = null
    }
  }

  function handleTooltipLeave() {
    hoveringTooltip.value = false
    // 立即隐藏
    hoveredUser.value = null
    activeId.value = null
    if (leaveTimer.value) {
      clearTimeout(leaveTimer.value) // 清理以防万一
      leaveTimer.value = null
    }
  }

  function clearHover() {
    if (movingUser.value) return
    hoveredUser.value = null
    activeId.value = null
  }

  function close() {
    // 关闭卡片时清理初始状态缓存
    initialState.value = null
    emit('update:show', false)
  }

  function avatarStyle(id) {
    if (!fading.value) return {}
    if (movingUser.value && id !== movingUser.value.id) {
      return {
        opacity: 0,
        transition: 'opacity 0.6s',
      }
    }
    return { transition: 'opacity 0.6s' }
  }

  function lineStyle(id) {
    if (!fading.value) return {}
    if (movingUser.value && id !== movingUser.value.id) {
      return { opacity: 0, transition: 'opacity 0.6s' }
    }
    return { transition: 'opacity 0.6s' }
  }

  function handleAnalyze(id) {
    // 根据用户的数据类型决定分析路由
    const user = localNearbyUsers.value.find(u => u.id === id) || localCenterUser.value
    if (!user) return

    // 使用analyze_id作为分析参数
    const analyzeId = user.analyze_id || id
    const encodedId = encodeURIComponent(analyzeId)

    // 现在所有网络用户都默认为scholar类型
    router.push({ path: `/scholar`, query: { user: encodedId } })
  }

  const originalUsers = ref([]) // 存储原始用户列表

  // 缓存已检测的头像URL结果
  const avatarCache = ref({})
  const defaultAvatar = '/image/avator.png'

  // 检测图片是否可用
  function checkImage(url) {
    if (!url || typeof url !== 'string' || url === defaultAvatar) {
      return Promise.resolve(false)
    }

    // 如果已经缓存过结果，直接返回
    if (avatarCache.value[url] !== undefined) {
      return Promise.resolve(avatarCache.value[url])
    }

    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        avatarCache.value[url] = true
        resolve(true)
      }
      img.onerror = () => {
        avatarCache.value[url] = false
        resolve(false)
      }
      img.src = url
    })
  }

  // 获取安全的头像URL (同步版本，使用缓存结果)
  function getSafeAvatarUrl(url) {
    if (!url || typeof url !== 'string') {
      return defaultAvatar
    }
    
    // 如果未缓存，触发异步检测并默认返回原URL
    if (avatarCache.value[url] === undefined) {
      checkImage(url) // 异步检测，不等待结果
      return url // 首次返回原URL
    }
    
    // 返回缓存的结果
    return avatarCache.value[url] ? url : defaultAvatar
  }

  // 监听localUsers变化时预加载检测所有头像
  watch(
    () => localUsers.value,
    async (newUsers) => {
      if (!newUsers || newUsers.length === 0) return
      
      // 预加载检测所有头像
      await Promise.all(newUsers.map(user => checkImage(user.avatar)))
    },
    { deep: true }
  )
  
  function resetToDefault() {
    if (!initialState.value) return

    // 恢复到真正的初始状态
    localCenterUser.value = { ...initialState.value.centerUser }
    localNearbyUsers.value = [...initialState.value.nearbyUsers]

    // 动画控制
    linesVisible.value = false
    fading.value = true

    nextTick(() => {
      setTimeout(() => {
        linesVisible.value = true
        fading.value = false
      }, 500)
    })
  }

  function handleAvatarError(event) {
    event.target.src = '/image/avator.png'
  }

  // 深色模式颜色计算
  function getLineColor() {
    return document.documentElement.classList.contains('dark') ? '#3E3E3E' : '#F9E1D8'
  }

  function getCenterBorderColor() {
    return document.documentElement.classList.contains('dark') ? '#686868' : '#CB7C5D'
  }

  function getAvatarBorderColor() {
    return document.documentElement.classList.contains('dark') ? '#3E3E3E' : '#F9E1D8'
  }
</script>

<style>
.loading-animation-container {
  position: relative;
  width: 400px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.network-loading-svg {
  animation: rotate 30s linear infinite;
}

.outer-circle-animation {
  fill: #FFFAF9;
  animation: pulse 3s ease-in-out infinite;
  transform-origin: center;
}

.inner-circle-animation {
  fill: none;
  animation: pulse-inverse 3s ease-in-out infinite;
  transform-origin: center;
}

.decorative-path-animation {
  fill: none;
  stroke: #CB7C5D;
  animation: pulse-stroke 3s ease-in-out infinite;
  transform-origin: center;
}

/* 中心节点样式 */
.center-node-animation {
  fill: #CB7C5D;
  animation: center-node-pulse 3s ease-in-out infinite;
  transform-origin: center;
}

/* 网络节点样式 */
.network-node-animation {
  fill: #F9E1D8;
  animation: node-pulse 3s ease-in-out infinite;
  transform-origin: center;
}

/* 为每个节点添加不同的动画延迟，创造波浪效果 */
.node-1 {
  animation-delay: 0.2s;
}

.node-2 {
  animation-delay: 0.4s;
}

.node-3 {
  animation-delay: 0.6s;
}

.node-4 {
  animation-delay: 0.8s;
}

.node-5 {
  animation-delay: 1s;
}

/* 连接线样式 */
.connection-line-animation {
  stroke: #F9E1D8;
  stroke-width: 2;
  animation: line-pulse 3s ease-in-out infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes pulse-inverse {
  0%, 100% {
    transform: scale(1.05);
    opacity: 1;
  }
  50% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

@keyframes pulse-stroke {
  0%, 100% {
    stroke-width: 21.8856;
    opacity: 0.8;
  }
  50% {
    stroke-width: 25;
    opacity: 1;
  }
}

@keyframes center-node-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.9;
  }
}

@keyframes node-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

@keyframes line-pulse {
  0%, 100% {
    stroke-width: 2;
    opacity: 0.6;
  }
  50% {
    stroke-width: 3;
    opacity: 1;
  }
}

/* 暗色模式支持 */
.dark .outer-circle-animation {
  fill: #1E1E1E;
}

.dark .inner-circle-animation {
  fill: #3C3C3C;
}

.dark .decorative-path-animation {
  stroke: #686868;
}

.dark .center-node-animation {
  fill: #686868;
}

.dark .network-node-animation {
  fill: #3E3E3E;
}

.dark .connection-line-animation {
  stroke: #3E3E3E;
}

/* 其他现有样式 */
.tooltip-avatar {
  width: 3.5rem; /* 对应 w-14 */
  height: 3.5rem; /* 对应 h-14 */
  border-radius: 50%; /* 圆形 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 12px 20px 0px #CB7C5D70;
}

.dark .tooltip-avatar {
  box-shadow: 0px 12px 20px 0px #00000050;
}

.dark .tooltip-description {
  color: #7A7A7A !important;
}

.tooltip-description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2em;
  max-height: 4.8em;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.line-transition {
  transition: all 0.8s ease-in-out;
}

.loading-text {
  animation: text-fade 2s ease-in-out infinite;
}

/* 提示过渡动画 */
.tip-fade-enter-active,
.tip-fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.tip-fade-enter-from,
.tip-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.tip-fade-enter-to,
.tip-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

@keyframes text-fade {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}


</style>
